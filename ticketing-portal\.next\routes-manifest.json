{"version": 3, "caseSensitive": false, "basePath": "/WeCare", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "redirects": [{"source": "/WeCare", "destination": "/WeCare/", "permanent": true, "basePath": false, "internal": true, "regex": "^\\/WeCare$"}, {"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "permanent": true, "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "regex": "^(?:\\/((?!\\.well-known(?:\\/.*)?)(?:[^/]+\\/)*[^/]+\\.\\w+))\\/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "permanent": true, "internal": true, "regex": "^(?:\\/((?!\\.well-known(?:\\/.*)?)(?:[^/]+\\/)*[^/\\.]+))$"}], "headers": []}